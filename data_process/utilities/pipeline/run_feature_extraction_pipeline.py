#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的SlowOnly特征提取流水线
用于BMN时间动作定位任务
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, description, cwd=None):
    """
    运行命令并处理结果
    
    Args:
        cmd (list): 命令列表
        description (str): 命令描述
        cwd (str): 工作目录
    
    Returns:
        bool: 是否成功
    """
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {description} 完成!")
            if result.stdout.strip():
                print("输出:")
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} 失败!")
            print("错误:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行 {description} 时出错: {e}")
        return False

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查必要的目录和文件
    required_paths = [
        '../../../data/MultiClassTAD/segmented_videos_ok',
        '../../../data/MultiClassTAD/segmented_videos_nok_electric',
        '../../../data/MultiClassTAD/segmented_videos_nok_appearance',
        '../../../mmaction2/tools/misc/clip_feature_extraction.py',
        '../../preprocessing/feature_extraction/slowonly_r50_feature_extraction_config.py'
    ]
    
    missing = []
    for path in required_paths:
        if not os.path.exists(path):
            missing.append(path)
    
    if missing:
        print("❌ 缺少必要的文件或目录:")
        for path in missing:
            print(f"   - {path}")
        return False
    
    print("✅ 前置条件检查通过")
    return True

def main():
    parser = argparse.ArgumentParser(description='SlowOnly特征提取完整流水线')
    parser.add_argument('--skip-extraction', action='store_true',
                       help='跳过特征提取步骤（如果已经提取过）')
    parser.add_argument('--skip-postprocess', action='store_true',
                       help='跳过特征后处理步骤')
    parser.add_argument('--only-validate', action='store_true',
                       help='只运行验证步骤')
    
    args = parser.parse_args()
    
    print("🎯 BMN时间动作定位 - SlowOnly特征提取流水线")
    print("=" * 60)
    
    # 如果只验证，直接跳到验证步骤
    if args.only_validate:
        print("🔍 只运行验证步骤...")
        success = run_command(['python', '../../utilities/validation/validate_features.py'], "验证特征格式")
        return
    
    # 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件检查失败，请先准备必要的文件和目录")
        return
    
    # 步骤1: 生成视频列表
    print("\n📝 步骤1: 生成视频列表文件")
    if not run_command(['python', '../../preprocessing/data_preparation/generate_video_list.py'], "生成视频列表"):
        return
    
    # 步骤2: 生成标注文件
    print("\n📝 步骤2: 生成ActivityNet格式标注文件")
    if not run_command(['python', '../../preprocessing/data_preparation/generate_activitynet_annotations.py'], "生成标注文件"):
        return
    
    # 步骤3: 特征提取（可选跳过）
    if not args.skip_extraction:
        print("\n🔧 步骤3: 提取SlowOnly特征")
        if not run_command(['python', '../../preprocessing/feature_extraction/extract_slowonly_features.py'], "提取SlowOnly特征"):
            print("⚠️  特征提取失败，可能需要手动处理")
            print("   请检查MMAction2环境和预训练模型下载")
            return
    else:
        print("\n⏭️  跳过特征提取步骤")
    
    # 步骤4: 特征后处理（可选跳过）
    if not args.skip_postprocess:
        print("\n🔄 步骤4: 后处理特征为BMN格式")
        if not run_command(['python', '../../preprocessing/feature_extraction/postprocess_features.py'], "后处理特征"):
            return
    else:
        print("\n⏭️  跳过特征后处理步骤")
    
    # 步骤5: 验证特征格式
    print("\n✅ 步骤5: 验证特征格式")
    if not run_command(['python', '../../utilities/validation/validate_features.py'], "验证特征格式"):
        return
    
    # 完成
    print("\n" + "=" * 60)
    print("🎉 特征提取流水线完成!")
    print("=" * 60)
    print("\n📋 生成的文件:")
    print("   📄 视频列表: ../data/MultiClassTAD/video_list.txt")
    print("   📄 训练标注: ../data/MultiClassTAD/multiclass_tad_train.json")
    print("   📄 验证标注: ../data/MultiClassTAD/multiclass_tad_val.json")
    print("   📁 特征文件: ../data/MultiClassTAD/features_slowonly/")
    print("   ⚙️  BMN配置: bmn_multiclass_tad_config.py")
    
    print("\n🚀 下一步:")
    print("   1. 使用BMN配置文件训练模型:")
    print("      cd ../mmaction2")
    print("      python tools/train.py ../data_process/bmn_multiclass_tad_config.py")
    print("   2. 或者测试现有BMN模型:")
    print("      python tools/test.py ../data_process/bmn_multiclass_tad_config.py [checkpoint.pth]")

if __name__ == '__main__':
    main()
