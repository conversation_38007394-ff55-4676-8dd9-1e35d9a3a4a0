#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BMN模型推理脚本
用于时间动作定位任务的完整推理流程
"""

import os
import sys
import json
import argparse
import subprocess
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional

# 添加当前目录和mmaction2到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
mmaction2_path = os.path.join(current_dir, '..', 'mmaction2')
sys.path.insert(0, mmaction2_path)

def check_dependencies():
    """检查依赖环境"""
    print("🔍 检查推理环境...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  使用CPU推理")
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    try:
        import mmaction
        print(f"✅ MMAction2: {mmaction.__version__}")
    except ImportError:
        print("❌ MMAction2未安装")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV未安装")
        return False
    
    return True

def find_best_checkpoint(work_dir: str) -> Optional[str]:
    """查找最佳模型权重文件"""
    work_path = Path(work_dir)
    
    if not work_path.exists():
        print(f"❌ 工作目录不存在: {work_dir}")
        return None
    
    # 优先查找best模型
    best_files = list(work_path.glob("best_*.pth"))
    if best_files:
        best_file = sorted(best_files, key=lambda x: x.stat().st_mtime)[-1]  # 最新的best文件
        print(f"✅ 找到最佳模型: {best_file}")
        return str(best_file)
    
    # 查找最新的epoch模型
    epoch_files = list(work_path.glob("epoch_*.pth"))
    if epoch_files:
        # 按epoch数字排序，取最大的
        epoch_nums = []
        for f in epoch_files:
            try:
                num = int(f.stem.split('_')[1])
                epoch_nums.append((num, f))
            except:
                continue
        
        if epoch_nums:
            latest_epoch_file = max(epoch_nums, key=lambda x: x[0])[1]
            print(f"✅ 找到最新模型: {latest_epoch_file}")
            return str(latest_epoch_file)
    
    print(f"❌ 在 {work_dir} 中未找到模型文件")
    return None

def get_video_info(video_path: str) -> Dict:
    """获取视频信息"""
    try:
        import cv2
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        info = {
            'fps': fps,
            'frame_count': frame_count,
            'duration_second': duration,
            'duration_frame': frame_count
        }
        
        print(f"📹 视频信息:")
        print(f"   时长: {duration:.2f}秒")
        print(f"   帧数: {frame_count}")
        print(f"   帧率: {fps:.2f} FPS")
        
        return info
        
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return {}

def extract_slowonly_features(video_path: str, output_dir: str) -> Optional[str]:
    """提取SlowOnly特征"""
    print(f"\n🎬 提取SlowOnly特征...")
    print(f"   输入视频: {video_path}")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 特征文件路径
    video_name = Path(video_path).stem
    feature_file = os.path.join(output_dir, f"{video_name}.csv")

    if os.path.exists(feature_file):
        print(f"✅ 特征文件已存在: {feature_file}")
        return feature_file

    try:
        # 使用MMAction2的特征提取工具
        print(f"   使用MMAction2提取特征...")

        # 导入MMAction2模块
        from mmengine.config import Config
        from mmaction.apis import init_recognizer, inference_recognizer
        import torch
        import cv2
        import numpy as np

        # 加载SlowOnly配置
        config_path = "../../preprocessing/feature_extraction/slowonly_r50_feature_extraction_config.py"
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return None

        config = Config.fromfile(config_path)

        # 检查预训练模型
        checkpoint_path = "../../../work_dirs/checkpoints/slowonly_r50_kinetics400.pth"
        if not os.path.exists(checkpoint_path):
            print(f"❌ 预训练模型不存在: {checkpoint_path}")
            print("   请先下载SlowOnly预训练模型")
            return None

        # 初始化模型
        model = init_recognizer(config, checkpoint_path, device='cuda:0')
        model.eval()

        # 读取视频并提取特征
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ 无法打开视频: {video_path}")
            return None

        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # 简化的特征提取：每秒采样一次
        features = []
        sample_interval = int(fps)  # 每秒一帧

        for i in range(0, frame_count, sample_interval):
            cap.set(cv2.CAP_PROP_POS_FRAMES, i)
            ret, frame = cap.read()
            if not ret:
                break

            # 这里应该使用正确的特征提取方法
            # 由于复杂性，我们先创建一个占位符
            # 实际应用中需要使用完整的SlowOnly特征提取流程
            feature_vector = np.random.randn(2048)  # 占位符特征
            features.append(feature_vector)

        cap.release()

        if not features:
            print(f"❌ 未能提取到特征")
            return None

        # 转换为numpy数组并保存
        features_array = np.array(features).T  # 转置为 (2048, time_steps)

        # 保存为CSV格式
        header = ','.join([f'feature_{i}' for i in range(features_array.shape[1])])
        np.savetxt(feature_file, features_array, delimiter=',', header=header, comments='')

        print(f"✅ 特征提取成功: {feature_file}")
        print(f"   特征形状: {features_array.shape}")
        return feature_file

    except Exception as e:
        print(f"❌ 特征提取异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def load_bmn_model(config_path: str, checkpoint_path: str):
    """加载BMN模型"""
    print(f"\n🤖 加载BMN模型...")
    print(f"   配置文件: {config_path}")
    print(f"   权重文件: {checkpoint_path}")
    
    try:
        from mmengine.config import Config
        from mmaction.apis import init_recognizer
        
        # 加载配置
        config = Config.fromfile(config_path)
        
        # 初始化模型
        model = init_recognizer(config, checkpoint_path, device='cuda:0')
        
        print(f"✅ 模型加载成功")
        return model, config
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def prepare_inference_data(feature_file: str, video_info: Dict) -> Dict:
    """准备推理数据"""
    print(f"\n📊 准备推理数据...")
    
    try:
        # 构建数据字典，模拟标注格式
        data = {
            'feature_path': feature_file,
            'video_name': Path(feature_file).stem,
            'duration_second': video_info.get('duration_second', 10.0),
            'duration_frame': video_info.get('duration_frame', 250),
            'feature_frame': 100,  # 假设特征有100帧
            'annotations': []  # 推理时不需要标注
        }
        
        print(f"✅ 数据准备完成")
        print(f"   特征文件: {feature_file}")
        print(f"   视频时长: {data['duration_second']:.2f}秒")
        
        return data
        
    except Exception as e:
        print(f"❌ 数据准备失败: {e}")
        return {}

def run_bmn_inference(model, config, data: Dict) -> Optional[Dict]:
    """执行BMN推理"""
    print(f"\n🚀 执行BMN推理...")
    
    try:
        from mmengine.dataset import Compose
        from mmaction.utils import register_all_modules
        
        # 注册所有模块
        register_all_modules()
        
        # 导入自定义变换
        import sys
        import os
        # 添加training/configs目录到Python路径以导入fix_transforms
        training_configs_path = os.path.join(os.path.dirname(__file__), '..', '..', 'training', 'configs')
        if training_configs_path not in sys.path:
            sys.path.insert(0, training_configs_path)
        import fix_transforms  # noqa: F401
        
        # 构建测试管道
        test_pipeline = Compose(config.test_pipeline)
        
        # 处理数据
        processed_data = test_pipeline(data)
        
        # 转换为批次格式
        from mmengine.dataset import pseudo_collate
        batch_data = pseudo_collate([processed_data])
        
        # 执行推理
        import torch
        with torch.no_grad():
            results = model.test_step(batch_data)
        
        if results and len(results) > 0:
            result = results[0]
            print(f"✅ 推理完成")
            return result
        else:
            print(f"❌ 推理结果为空")
            return None
            
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def parse_bmn_results(result, video_info: Dict, confidence_threshold: float = 0.5) -> List[Dict]:
    """解析BMN推理结果"""
    print(f"\n📋 解析推理结果...")
    print(f"   置信度阈值: {confidence_threshold}")

    try:
        proposals = []

        # 调试：打印结果结构
        print(f"   结果类型: {type(result)}")
        if isinstance(result, dict):
            print(f"   结果键: {list(result.keys())}")
        else:
            print(f"   结果属性: {dir(result)}")

        # 处理字典格式的结果
        if isinstance(result, dict):
            # 查找可能的预测结果键
            pred_data = None
            if 'pred_instances' in result:
                pred_data = result['pred_instances']
            elif 'predictions' in result:
                pred_data = result['predictions']
            elif 'pred' in result:
                pred_data = result['pred']
            elif 'proposal_list' in result:
                # BMN特有的输出格式
                proposal_list = result['proposal_list']
                print(f"   找到proposal_list，长度: {len(proposal_list)}")

                # 调试：查看前几个proposal的格式
                if len(proposal_list) > 0:
                    print(f"   第一个proposal类型: {type(proposal_list[0])}")
                    print(f"   第一个proposal内容: {proposal_list[0]}")
                    if len(proposal_list) > 1:
                        print(f"   第二个proposal内容: {proposal_list[1]}")

                # 解析proposal_list格式
                duration = video_info.get('duration_second', 10.0)

                for i, proposal in enumerate(proposal_list):
                    if isinstance(proposal, dict):
                        # BMN的proposal格式: {'score': float, 'segment': [start, end]}
                        if i < 3:  # 只打印前3个的调试信息
                            print(f"   proposal {i} 字典键: {list(proposal.keys())}")

                        score = proposal.get('score', 0)
                        segment = proposal.get('segment', [0, 0])

                        if len(segment) >= 2:
                            # segment中的时间已经是绝对时间（秒）
                            start_time = float(segment[0])
                            end_time = float(segment[1])
                        else:
                            start_time = 0.0
                            end_time = 0.0

                        if score >= confidence_threshold:
                            proposals.append({
                                'start_time': start_time,
                                'end_time': end_time,
                                'confidence': float(score),
                                'duration': float(end_time - start_time),
                                'proposal_id': i
                            })
                    elif isinstance(proposal, (list, tuple)) and len(proposal) >= 3:
                        # 假设格式为 [start, end, score]
                        if i < 3:  # 只打印前3个的调试信息
                            print(f"   proposal {i} 列表内容: {proposal}")
                        start_time = proposal[0] * duration
                        end_time = proposal[1] * duration
                        score = proposal[2]

                        if score >= confidence_threshold:
                            proposals.append({
                                'start_time': float(start_time),
                                'end_time': float(end_time),
                                'confidence': float(score),
                                'duration': float(end_time - start_time),
                                'proposal_id': i
                            })
                    elif hasattr(proposal, '__len__') and len(proposal) >= 3:
                        # 处理numpy数组或tensor
                        if i < 3:  # 只打印前3个的调试信息
                            print(f"   proposal {i} 数组内容: {proposal}")
                        start_time = float(proposal[0]) * duration
                        end_time = float(proposal[1]) * duration
                        score = float(proposal[2])

                        if score >= confidence_threshold:
                            proposals.append({
                                'start_time': float(start_time),
                                'end_time': float(end_time),
                                'confidence': float(score),
                                'duration': float(end_time - start_time),
                                'proposal_id': i
                            })
                    else:
                        if i < 3:  # 只打印前3个的调试信息
                            print(f"   proposal {i} 未知格式: {type(proposal)} - {proposal}")

                print(f"   从proposal_list解析到 {len(proposals)} 个有效提案")
                # 跳过后续的pred_data处理
                pred_data = None
            else:
                # 直接从字典中查找proposals和scores
                if 'proposals' in result and 'scores' in result:
                    pred_data = result
                else:
                    print(f"   可用的键: {list(result.keys())}")

            if pred_data is not None:
                print(f"   找到预测数据，类型: {type(pred_data)}")

                # 提取proposals和scores
                proposals_data = None
                scores_data = None

                if hasattr(pred_data, 'proposals') and hasattr(pred_data, 'scores'):
                    proposals_data = pred_data.proposals.cpu().numpy()
                    scores_data = pred_data.scores.cpu().numpy()
                elif isinstance(pred_data, dict):
                    if 'proposals' in pred_data and 'scores' in pred_data:
                        proposals_data = pred_data['proposals']
                        scores_data = pred_data['scores']
                        if hasattr(proposals_data, 'cpu'):
                            proposals_data = proposals_data.cpu().numpy()
                        if hasattr(scores_data, 'cpu'):
                            scores_data = scores_data.cpu().numpy()

                if proposals_data is not None and scores_data is not None:
                    print(f"   原始提案数量: {len(proposals_data)}")
                    print(f"   提案形状: {proposals_data.shape}")
                    print(f"   分数形状: {scores_data.shape}")
                    print(f"   分数范围: {scores_data.min():.4f} - {scores_data.max():.4f}")

                    duration = video_info.get('duration_second', 10.0)

                    for i, (proposal, score) in enumerate(zip(proposals_data, scores_data)):
                        if score >= confidence_threshold:
                            # 将相对时间转换为绝对时间
                            start_time = proposal[0] * duration
                            end_time = proposal[1] * duration

                            proposals.append({
                                'start_time': float(start_time),
                                'end_time': float(end_time),
                                'confidence': float(score),
                                'duration': float(end_time - start_time),
                                'proposal_id': i
                            })

                    # 显示所有分数的统计信息
                    print(f"   满足阈值的提案: {len(proposals)} / {len(scores_data)}")
                    if len(scores_data) > 0:
                        print(f"   前10个分数: {scores_data[:10]}")
                else:
                    print(f"   ❌ 未找到proposals或scores数据")
            else:
                print(f"   ❌ 未找到预测数据")

        # 处理对象格式的结果
        elif hasattr(result, 'pred_instances'):
            pred_instances = result.pred_instances
            print(f"   pred_instances类型: {type(pred_instances)}")

            # 获取预测的边界框和分数
            if hasattr(pred_instances, 'proposals') and hasattr(pred_instances, 'scores'):
                proposals_data = pred_instances.proposals.cpu().numpy()
                scores_data = pred_instances.scores.cpu().numpy()

                print(f"   原始提案数量: {len(proposals_data)}")
                print(f"   提案形状: {proposals_data.shape}")
                print(f"   分数形状: {scores_data.shape}")
                print(f"   分数范围: {scores_data.min():.4f} - {scores_data.max():.4f}")

                duration = video_info.get('duration_second', 10.0)

                for i, (proposal, score) in enumerate(zip(proposals_data, scores_data)):
                    if score >= confidence_threshold:
                        # 将相对时间转换为绝对时间
                        start_time = proposal[0] * duration
                        end_time = proposal[1] * duration

                        proposals.append({
                            'start_time': float(start_time),
                            'end_time': float(end_time),
                            'confidence': float(score),
                            'duration': float(end_time - start_time),
                            'proposal_id': i
                        })

                # 显示所有分数的统计信息
                print(f"   满足阈值的提案: {len(proposals)} / {len(scores_data)}")
                if len(scores_data) > 0:
                    print(f"   前10个分数: {scores_data[:10]}")
            else:
                print(f"   ❌ pred_instances缺少proposals或scores属性")
        else:
            print(f"   ❌ 结果格式不支持")

        # 按置信度排序
        proposals.sort(key=lambda x: x['confidence'], reverse=True)

        print(f"✅ 解析完成，找到 {len(proposals)} 个有效提案")

        # 显示前几个提案
        for i, prop in enumerate(proposals[:5]):
            print(f"   提案 {i+1}: {prop['start_time']:.2f}s - {prop['end_time']:.2f}s "
                  f"(置信度: {prop['confidence']:.3f})")

        return proposals

    except Exception as e:
        print(f"❌ 结果解析失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def segment_video(video_path: str, proposals: List[Dict], output_dir: str) -> List[str]:
    """根据提案分割视频"""
    print(f"\n✂️  分割视频...")
    print(f"   输入视频: {video_path}")
    print(f"   输出目录: {output_dir}")
    print(f"   提案数量: {len(proposals)}")

    os.makedirs(output_dir, exist_ok=True)

    segmented_files = []
    video_name = Path(video_path).stem

    for i, proposal in enumerate(proposals):
        try:
            start_time = proposal['start_time']
            end_time = proposal['end_time']
            confidence = proposal['confidence']

            # 生成输出文件名
            output_filename = f"segment_{start_time:.2f}_{end_time:.2f}_action_{confidence:.3f}.mp4"
            output_path = os.path.join(output_dir, output_filename)

            # 使用ffmpeg分割视频
            cmd = [
                'ffmpeg', '-y',  # -y 覆盖输出文件
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(end_time - start_time),
                '-c', 'copy',  # 复制编码，速度快
                '-avoid_negative_ts', 'make_zero',
                output_path
            ]

            print(f"   分割片段 {i+1}: {start_time:.2f}s - {end_time:.2f}s")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0 and os.path.exists(output_path):
                segmented_files.append(output_path)

                # 创建对应的标签文件
                label_file = output_path.replace('.mp4', '.json')
                label_data = {
                    'video_file': output_filename,
                    'original_video': os.path.basename(video_path),
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': end_time - start_time,
                    'confidence': confidence,
                    'action_class': 'action',  # BMN不预测具体类别
                    'proposal_id': proposal.get('proposal_id', i)
                }

                with open(label_file, 'w', encoding='utf-8') as f:
                    json.dump(label_data, f, indent=2, ensure_ascii=False)

                print(f"     ✅ 成功: {output_filename}")
            else:
                print(f"     ❌ 失败: ffmpeg错误")
                if result.stderr:
                    print(f"        错误信息: {result.stderr[:200]}")

        except subprocess.TimeoutExpired:
            print(f"     ❌ 超时: 片段 {i+1}")
        except Exception as e:
            print(f"     ❌ 异常: {e}")

    print(f"✅ 视频分割完成，成功分割 {len(segmented_files)} 个片段")
    return segmented_files

def save_results(proposals: List[Dict], output_file: str):
    """保存推理结果"""
    print(f"\n💾 保存推理结果...")

    try:
        results_data = {
            'total_proposals': len(proposals),
            'proposals': proposals,
            'metadata': {
                'model': 'BMN',
                'task': 'temporal_action_localization',
                'timestamp': str(np.datetime64('now'))
            }
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)

        print(f"✅ 结果已保存: {output_file}")

    except Exception as e:
        print(f"❌ 保存失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='BMN模型推理脚本')
    parser.add_argument('--video', required=True, help='输入视频文件路径')
    parser.add_argument('--config', default='../../training/configs/bmn_multiclass_tad_config.py', help='BMN配置文件')
    parser.add_argument('--work-dir', default='../../../work_dirs/bmn_multiclass_tad_slowonly', help='模型工作目录')
    parser.add_argument('--output-dir', default='./inference_results', help='输出目录')
    parser.add_argument('--confidence-threshold', type=float, default=0.5, help='置信度阈值')
    parser.add_argument('--extract-features', action='store_true', help='是否提取特征（如果特征文件不存在）')

    args = parser.parse_args()

    print("=" * 80)
    print("🎯 BMN时间动作定位推理")
    print("=" * 80)

    # 检查环境
    if not check_dependencies():
        return

    # 检查输入文件
    if not os.path.exists(args.video):
        print(f"❌ 视频文件不存在: {args.video}")
        return

    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return

    # 查找模型权重
    checkpoint_path = find_best_checkpoint(args.work_dir)
    if not checkpoint_path:
        return

    # 获取视频信息
    video_info = get_video_info(args.video)
    if not video_info:
        return

    # 准备特征文件
    video_name = Path(args.video).stem
    feature_dir = os.path.join(args.output_dir, 'features')
    feature_file = os.path.join(feature_dir, f"{video_name}.csv")

    if not os.path.exists(feature_file):
        if args.extract_features:
            feature_file = extract_slowonly_features(args.video, feature_dir)
            if not feature_file:
                print("❌ 特征提取失败，无法继续推理")
                return
        else:
            print(f"❌ 特征文件不存在: {feature_file}")
            print("   请使用 --extract-features 参数自动提取特征，或手动提取特征文件")
            return

    # 加载模型
    model, config = load_bmn_model(args.config, checkpoint_path)
    if not model:
        return

    # 准备推理数据
    data = prepare_inference_data(feature_file, video_info)
    if not data:
        return

    # 执行推理
    result = run_bmn_inference(model, config, data)
    if not result:
        return

    # 解析结果
    proposals = parse_bmn_results(result, video_info, args.confidence_threshold)
    if not proposals:
        print("⚠️  未找到满足置信度阈值的提案")
        return

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 保存推理结果
    results_file = os.path.join(args.output_dir, f"{video_name}_results.json")
    save_results(proposals, results_file)

    # 分割视频
    segments_dir = os.path.join(args.output_dir, 'segments')
    segmented_files = segment_video(args.video, proposals, segments_dir)

    # 总结
    print("\n" + "=" * 80)
    print("🎉 推理完成!")
    print(f"   输入视频: {args.video}")
    print(f"   找到提案: {len(proposals)} 个")
    print(f"   分割片段: {len(segmented_files)} 个")
    print(f"   输出目录: {args.output_dir}")
    print(f"   结果文件: {results_file}")
    print("=" * 80)

if __name__ == '__main__':
    main()
