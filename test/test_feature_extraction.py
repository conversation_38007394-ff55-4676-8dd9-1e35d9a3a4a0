#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SlowOnly特征提取脚本测试
用于验证脚本功能和环境配置
"""

import os
import sys
import json
import subprocess
from pathlib import Path
import numpy as np

def test_environment():
    """测试环境配置"""
    print("🔍 测试环境配置...")
    
    # 检查Python环境
    print(f"Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ['torch', 'numpy', 'mmengine', 'mmaction']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n❌ 缺少必要的包: {missing_packages}")
        print("请确保已激活mmaction2-tad环境")
        return False
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA: 可用 (设备数量: {torch.cuda.device_count()})")
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA: 不可用，将使用CPU")
    except:
        print("❌ 无法检查CUDA状态")
    
    return True

def test_input_files():
    """测试输入文件"""
    print("\n🔍 测试输入文件...")
    
    # 检查重构JSON文件
    train_json = Path('../data/MultiClassTAD/multiclass_tad_train_reconstructed.json')
    val_json = Path('../data/MultiClassTAD/multiclass_tad_val_reconstructed.json')
    raw_videos_root = Path('../data/raw_videos')
    
    files_to_check = [
        (train_json, "训练集JSON文件"),
        (val_json, "验证集JSON文件"),
        (raw_videos_root, "原始视频目录")
    ]
    
    all_exist = True
    for file_path, description in files_to_check:
        if file_path.exists():
            print(f"✅ {description}: {file_path}")
        else:
            print(f"❌ {description}: {file_path} (不存在)")
            all_exist = False
    
    if not all_exist:
        return False
    
    # 检查JSON文件内容
    try:
        with open(train_json, 'r', encoding='utf-8') as f:
            train_data = json.load(f)
        print(f"✅ 训练集包含 {len(train_data)} 个视频")
        
        with open(val_json, 'r', encoding='utf-8') as f:
            val_data = json.load(f)
        print(f"✅ 验证集包含 {len(val_data)} 个视频")
        
        # 检查视频文件
        all_videos = set(train_data.keys()) | set(val_data.keys())
        existing_videos = 0
        missing_videos = []
        
        for video_name in all_videos:
            video_path = raw_videos_root / f"{video_name}.mp4"
            if video_path.exists():
                existing_videos += 1
            else:
                missing_videos.append(video_name)
        
        print(f"✅ 找到 {existing_videos}/{len(all_videos)} 个视频文件")
        
        if missing_videos:
            print(f"⚠️  缺少 {len(missing_videos)} 个视频文件:")
            for video in missing_videos[:3]:  # 只显示前3个
                print(f"   - {video}.mp4")
            if len(missing_videos) > 3:
                print(f"   ... 还有 {len(missing_videos) - 3} 个")
        
        return existing_videos > 0
        
    except Exception as e:
        print(f"❌ 读取JSON文件失败: {e}")
        return False

def test_mmaction2_tools():
    """测试MMAction2工具"""
    print("\n🔍 测试MMAction2工具...")
    
    # 检查MMAction2目录
    mmaction2_dir = Path('../mmaction2')
    if not mmaction2_dir.exists():
        print(f"❌ MMAction2目录不存在: {mmaction2_dir}")
        return False
    
    # 检查特征提取工具
    clip_tool = mmaction2_dir / 'tools/misc/clip_feature_extraction.py'
    if clip_tool.exists():
        print(f"✅ 特征提取工具: {clip_tool}")
    else:
        print(f"❌ 特征提取工具不存在: {clip_tool}")
        return False
    
    # 测试工具是否可以运行
    try:
        cmd = ['python', str(clip_tool), '--help']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 特征提取工具可以正常运行")
        else:
            print("❌ 特征提取工具运行失败")
            print(f"错误: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⚠️  特征提取工具响应超时")
    except Exception as e:
        print(f"❌ 测试特征提取工具时出错: {e}")
        return False
    
    return True

def test_feature_extraction_script():
    """测试特征提取脚本"""
    print("\n🔍 测试特征提取脚本...")
    
    script_path = Path('extract_slowonly_features_for_bmn.py')
    if not script_path.exists():
        print(f"❌ 特征提取脚本不存在: {script_path}")
        return False
    
    print(f"✅ 特征提取脚本: {script_path}")
    
    # 测试脚本帮助信息
    try:
        cmd = ['python', str(script_path), '--help']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 特征提取脚本可以正常运行")
            return True
        else:
            print("❌ 特征提取脚本运行失败")
            print(f"错误: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⚠️  特征提取脚本响应超时")
        return False
    except Exception as e:
        print(f"❌ 测试特征提取脚本时出错: {e}")
        return False

def test_output_directory():
    """测试输出目录权限"""
    print("\n🔍 测试输出目录权限...")
    
    output_dir = Path('../data/MultiClassTAD/features_slowonly_reconstructed')
    
    try:
        # 创建测试目录
        test_dir = output_dir / 'test'
        test_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试文件
        test_file = test_dir / 'test.txt'
        with open(test_file, 'w') as f:
            f.write('test')
        
        # 读取测试文件
        with open(test_file, 'r') as f:
            content = f.read()
        
        # 删除测试文件和目录
        test_file.unlink()
        test_dir.rmdir()
        
        print(f"✅ 输出目录权限正常: {output_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 输出目录权限测试失败: {e}")
        return False

def run_dry_run_test():
    """运行干运行测试"""
    print("\n🔍 运行干运行测试...")
    
    # 创建一个小的测试JSON文件
    test_data = {
        "test_video": {
            "duration_second": 10.0,
            "duration_frame": 250,
            "annotations": [
                {
                    "segment": [2.0, 5.0],
                    "label": "OK_Action"
                }
            ]
        }
    }
    
    test_json = Path('test_data.json')
    try:
        with open(test_json, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2)
        
        print("✅ 创建测试数据文件")
        
        # 这里可以添加更多的干运行测试
        # 比如测试配置文件生成、特征后处理等
        
        # 清理测试文件
        test_json.unlink()
        print("✅ 清理测试文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 干运行测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 SlowOnly特征提取脚本测试")
    print("=" * 60)
    
    tests = [
        ("环境配置", test_environment),
        ("输入文件", test_input_files),
        ("MMAction2工具", test_mmaction2_tools),
        ("特征提取脚本", test_feature_extraction_script),
        ("输出目录权限", test_output_directory),
        ("干运行测试", run_dry_run_test)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("🧪 测试总结")
    print("=" * 60)
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！可以开始特征提取。")
        print("\n🚀 运行特征提取:")
        print("   python extract_slowonly_features_for_bmn.py")
        return True
    else:
        print("⚠️  部分测试失败，请检查环境配置。")
        print("\n🔧 建议:")
        print("   1. 确保已激活mmaction2-tad环境")
        print("   2. 检查输入文件路径")
        print("   3. 确认MMAction2安装正确")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
