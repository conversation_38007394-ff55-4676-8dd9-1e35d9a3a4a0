# BMN配置文件模板 - 基于重构数据集
# 自动生成于SlowOnly特征提取脚本

_base_ = [
    '../mmaction2/configs/_base_/models/bmn_400x100.py',
    '../mmaction2/configs/_base_/default_runtime.py'
]

# 模型设置 - 修改特征维度为2048
model = dict(
    type='BMN',
    temporal_dim=100,           # 时间维度：100个时间步
    boundary_ratio=0.5,
    num_samples=32,
    num_samples_per_bin=3,
    feat_dim=2048,              # SlowOnly R50特征维度
    soft_nms_alpha=0.4,
    soft_nms_low_threshold=0.5,
    soft_nms_high_threshold=0.9,
    post_process_top_k=100)

# 数据集设置
dataset_type = 'ActivityNetDataset'
data_root = '../data/MultiClassTAD/features_slowonly_reconstructed/features_csv'
data_root_val = '../data/MultiClassTAD/features_slowonly_reconstructed/features_csv'
ann_file_train = '../data/MultiClassTAD/multiclass_tad_train_reconstructed.json'
ann_file_val = '../data/MultiClassTAD/multiclass_tad_val_reconstructed.json'
ann_file_test = '../data/MultiClassTAD/multiclass_tad_val_reconstructed.json'

# 训练数据流水线
train_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', ))
]

# 验证数据流水线
val_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', ))
]

# 测试数据流水线
test_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        type='PackLocalizationInputs',
        keys=(),
        meta_keys=('video_name', ))
]

# 数据加载器
train_dataloader = dict(
    batch_size=8,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_train,
        data_prefix=dict(video=data_root),
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=1,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_val,
        data_prefix=dict(video=data_root_val),
        pipeline=val_pipeline,
        test_mode=True))

test_dataloader = dict(
    batch_size=1,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_test,
        data_prefix=dict(video=data_root_val),
        pipeline=test_pipeline,
        test_mode=True))

# 优化器
optim_wrapper = dict(
    optimizer=dict(type='Adam', lr=0.001, weight_decay=1e-4),
    clip_grad=dict(max_norm=40, norm_type=2))

# 学习率调度器
param_scheduler = [
    dict(type='MultiStepLR', begin=0, end=9, by_epoch=True, milestones=[7], gamma=0.1)
]

# 训练配置
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=9, val_begin=1, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# 工作目录
work_dir = '../work_dirs/bmn_reconstructed_slowonly'

# 评估器
test_evaluator = dict(
    type='ANetMetric',
    metric_type='AR@AN',
    dump_config=dict(out=f'{work_dir}/results.json', output_format='json'))
val_evaluator = test_evaluator

# 默认钩子
default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=10, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=1, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

# 环境配置
env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='ActionVisualizer', vis_backends=vis_backends, name='visualizer')

log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)
log_level = 'INFO'
load_from = None
resume = False
