# TAD数据集重构工具使用说明

本工具集用于将预分割的动作片段重构为符合MMAction2 TAD任务要求的完整视频数据集。

## 问题背景

原始数据准备方式存在的问题：
- 使用预分割的1-3秒动作片段进行TAD训练
- 每个片段的annotation segment都覆盖整个片段长度
- 模型无法学习区分动作和背景，导致推理输出异常长的时间段

## 解决方案

重构数据集，使用完整的原始视频配合精确的时间标注：
- 将预分割片段的时间信息映射回原始完整视频
- 生成符合ActivityNetDataset格式的标注文件
- 为完整视频重新提取特征

## 工具组成

### 1. `reconstruct_tad_dataset.py`
主要的数据集重构脚本，功能包括：
- 解析预分割片段文件名，提取时间信息
- 匹配对应的原始视频文件
- 生成符合MMAction2标准的JSON标注文件

### 2. `validate_reconstructed_dataset.py`
数据集验证脚本，功能包括：
- 验证数据格式是否符合ActivityNetDataset标准
- 分析时间分布和标签分布
- 检查时间重叠问题
- 生成统计可视化图表

### 3. `extract_features_for_reconstructed.py`
特征提取脚本，功能包括：
- 为完整视频重新提取特征
- 支持SlowOnly模型和自定义特征提取方法
- 更新标注文件中的特征路径信息

### 4. `run_reconstruction.py`
一键运行脚本，整合所有流程

## 使用步骤

### 步骤1: 准备环境

确保以下目录结构存在：
```
/home/<USER>/johnny_ws/mmaction2_ws/
├── data/
│   ├── MultiClassTAD/                    # 当前数据集目录
│   │   ├── segmented_videos_ok/          # OK动作片段
│   │   ├── segmented_videos_nok_appearance/  # NOK外观片段
│   │   ├── segmented_videos_nok_electric/    # NOK电气片段
│   │   └── multiclass_tad_train.json    # 原始标注文件
│   └── raw_videos/                       # 原始完整视频目录
└── test/                                 # 本工具集目录
```

### 步骤2: 运行数据集重构

```bash
cd /home/<USER>/johnny_ws/mmaction2_ws/test

# 激活conda环境
conda activate mmaction2-tad

# 运行完整重构流程
python run_reconstruction.py

# 或者指定自定义参数
python run_reconstruction.py \
    --data-root /path/to/MultiClassTAD \
    --raw-videos-root /path/to/raw_videos \
    --train-ratio 0.8
```

### 步骤3: 验证重构结果

```bash
# 单独运行验证（如果之前跳过了验证）
python run_reconstruction.py --only-validate

# 或者直接运行验证脚本
python validate_reconstructed_dataset.py
```

### 步骤4: 提取特征

```bash
# 使用自定义方法提取特征（推荐）
python extract_features_for_reconstructed.py --method custom

# 或使用SlowOnly模型（需要预训练权重）
python extract_features_for_reconstructed.py --method slowonly
```

### 步骤5: 更新MMAction2配置

重构完成后，需要更新训练配置文件：

```python
# 更新数据集路径
ann_file_train = 'data/MultiClassTAD/multiclass_tad_train_reconstructed.json'
ann_file_val = 'data/MultiClassTAD/multiclass_tad_val_reconstructed.json'

# 更新特征路径
data_prefix = 'data/MultiClassTAD/features_reconstructed'

# 调整模型参数以适应更长的视频
model = dict(
    type='BMN',
    temporal_dim=300,  # 根据实际视频长度调整
    num_samples=100,
    # 其他参数保持不变
)
```

## 输出文件说明

### 重构后的标注文件
- `multiclass_tad_train_reconstructed.json`: 训练集标注
- `multiclass_tad_val_reconstructed.json`: 验证集标注

标注格式示例：
```json
{
  "20250727T075411Z_20250727T075911Z": {
    "duration_second": 300.0,
    "duration_frame": 7500,
    "annotations": [
      {
        "segment": [45.2, 47.8],
        "label": "OK_Action"
      },
      {
        "segment": [123.5, 125.1],
        "label": "NOK_Appearance"
      }
    ],
    "feature_frame": 3000,
    "fps": 25.0,
    "rfps": 25.0,
    "original_video_path": "/path/to/original/video.mp4"
  }
}
```

### 特征文件
- `features_reconstructed/`: 重新提取的特征文件目录
- 每个视频对应一个CSV文件，包含时序特征

### 验证报告
- `dataset_analysis.png`: 数据集统计可视化图表
- 控制台输出详细的验证报告

## 常见问题

### Q1: 文件名解析失败
**问题**: 警告信息显示无法解析某些文件名
**解决**: 检查文件名是否符合预期格式，必要时修改`parse_filename`方法中的正则表达式

### Q2: 找不到原始视频
**问题**: 警告信息显示未找到对应的原始视频
**解决**: 
- 检查原始视频目录路径是否正确
- 确认原始视频文件名包含时间戳信息
- 必要时修改`find_original_video`方法的匹配逻辑

### Q3: 特征提取失败
**问题**: 特征提取过程出错
**解决**:
- 确保安装了必要的依赖（opencv-python, numpy等）
- 检查视频文件是否可以正常打开
- 尝试使用不同的特征提取方法

### Q4: 时间重叠问题
**问题**: 验证时发现时间重叠
**解决**: 这通常是正常的，因为原始标注可能包含重叠的动作片段

## 参数说明

### run_reconstruction.py参数
- `--data-root`: 数据集根目录路径
- `--raw-videos-root`: 原始视频目录路径  
- `--train-ratio`: 训练集比例（默认0.8）
- `--skip-validation`: 跳过验证步骤
- `--only-validate`: 仅运行验证

### extract_features_for_reconstructed.py参数
- `--data-root`: 数据集根目录
- `--mmaction2-root`: MMAction2根目录
- `--method`: 特征提取方法（slowonly/custom）

## 预期改进效果

使用重构后的数据集训练，预期能够：
- 将时间定位精度从0-59秒异常输出改善到1-3秒精确定位
- AR@1指标从接近0提升到0.3-0.5
- 提高模型在真实长视频场景中的泛化能力

## 技术支持

如遇到问题，请检查：
1. 文件路径是否正确
2. 依赖包是否完整安装
3. 视频文件是否可以正常访问
4. 控制台错误信息和日志输出
