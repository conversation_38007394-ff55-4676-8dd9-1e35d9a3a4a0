# SlowOnly特征提取脚本使用说明

## 概述

`extract_slowonly_features_for_bmn.py` 脚本用于从 `run_reconstruction.py` 生成的重构JSON文件中提取原始视频的SlowOnly特征，用于BMN模型训练。

## 功能特点

- ✅ 自动从重构JSON文件读取原始视频信息
- ✅ 使用MMAction2官方SlowOnly模型提取特征
- ✅ 自动下载预训练模型
- ✅ 特征后处理为BMN所需的100x2048格式
- ✅ 特征文件格式验证
- ✅ 自动生成BMN配置文件模板

## 环境要求

确保已激活 `mmaction2-tad` conda环境：

```bash
conda activate mmaction2-tad
```

## 使用方法

### 1. 基本用法

```bash
cd /home/<USER>/johnny_ws/mmaction2_ws/test
python extract_slowonly_features_for_bmn.py
```

### 2. 自定义参数

```bash
python extract_slowonly_features_for_bmn.py \
    --train-json ../data/MultiClassTAD/multiclass_tad_train_reconstructed.json \
    --val-json ../data/MultiClassTAD/multiclass_tad_val_reconstructed.json \
    --raw-videos-root ../data/raw_videos \
    --output-dir ../data/MultiClassTAD/features_slowonly_reconstructed
```

### 3. 跳过某些步骤

```bash
# 跳过特征提取（如果已经提取过）
python extract_slowonly_features_for_bmn.py --skip-extraction

# 跳过特征后处理
python extract_slowonly_features_for_bmn.py --skip-postprocess

# 只运行验证
python extract_slowonly_features_for_bmn.py --only-validate
```

## 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--train-json` | `../data/MultiClassTAD/multiclass_tad_train_reconstructed.json` | 训练集重构JSON文件路径 |
| `--val-json` | `../data/MultiClassTAD/multiclass_tad_val_reconstructed.json` | 验证集重构JSON文件路径 |
| `--raw-videos-root` | `../data/raw_videos` | 原始视频文件根目录路径 |
| `--output-dir` | `../data/MultiClassTAD/features_slowonly_reconstructed` | 输出特征文件目录 |
| `--skip-extraction` | False | 跳过特征提取步骤 |
| `--skip-postprocess` | False | 跳过特征后处理步骤 |
| `--only-validate` | False | 只运行验证步骤 |

## 输出文件结构

```
features_slowonly_reconstructed/
├── features_raw/           # 原始pkl特征文件
│   ├── video1.pkl
│   ├── video2.pkl
│   └── ...
├── features_csv/           # 处理后的CSV特征文件
│   ├── video1.csv         # 100x2048格式
│   ├── video2.csv
│   └── ...
└── bmn_reconstructed_config.py  # 自动生成的BMN配置文件
```

## 特征格式说明

- **原始特征**: SlowOnly R50模型提取的2048维特征
- **处理后特征**: 池化到100个时间步，每个时间步2048维
- **文件格式**: CSV格式，便于BMN模型加载

## 工作流程

1. **加载重构数据**: 从JSON文件读取视频信息
2. **特征提取**: 使用SlowOnly模型提取原始特征
3. **特征后处理**: 池化到100x2048格式
4. **格式验证**: 确保特征文件格式正确
5. **配置生成**: 自动生成BMN训练配置文件

## 故障排除

### 1. 下载模型失败

```bash
# 手动下载预训练模型
mkdir -p ../work_dirs/checkpoints
wget -O ../work_dirs/checkpoints/slowonly_r50_kinetics400.pth \
  https://download.openmmlab.com/mmaction/v1.0/recognition/slowonly/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-rgb/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-rgb_20220901-e7b65fad.pth
```

### 2. 视频文件不存在

确保原始视频文件位于正确的目录中：

```bash
ls ../data/raw_videos/
```

### 3. 内存不足

如果遇到内存不足问题，可以：
- 减少batch size
- 分批处理视频
- 使用更小的clip_interval

### 4. CUDA错误

确保CUDA环境正确配置：

```bash
python -c "import torch; print(torch.cuda.is_available())"
```

## 下一步操作

特征提取完成后：

1. **检查特征文件**:
   ```bash
   python extract_slowonly_features_for_bmn.py --only-validate
   ```

2. **训练BMN模型**:
   ```bash
   cd ../mmaction2
   python tools/train.py ../test/bmn_reconstructed_config.py
   ```

3. **监控训练过程**:
   ```bash
   tensorboard --logdir ../work_dirs/bmn_reconstructed_slowonly
   ```

## 技术细节

### SlowOnly模型配置
- **模型**: ResNet3dSlowOnly (depth=50)
- **预训练**: Kinetics-400数据集
- **输入**: 8帧，224x224分辨率
- **输出**: 2048维特征向量

### 特征提取参数
- **长视频模式**: 启用，支持任意长度视频
- **时间采样**: clip_interval=16, frame_interval=1
- **空间池化**: 平均池化
- **时间池化**: 保持原始时间维度

### 特征后处理
- **目标格式**: 100个时间步 × 2048维特征
- **池化方法**: 时间维度平均池化
- **插值方法**: 线性插值（当时间步不足时）

## 注意事项

1. **环境要求**: 必须在mmaction2-tad环境中运行
2. **磁盘空间**: 确保有足够空间存储特征文件
3. **GPU内存**: 建议使用8GB以上显存的GPU
4. **网络连接**: 首次运行需要下载预训练模型

## 联系支持

如果遇到问题，请检查：
1. 环境配置是否正确
2. 输入文件路径是否存在
3. 磁盘空间是否充足
4. GPU内存是否足够
