#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于重构JSON文件的SlowOnly特征提取脚本
用于BMN时间动作定位任务

该脚本从run_reconstruction.py生成的JSON文件中读取原始视频信息，
对这些原始视频进行SlowOnly特征提取，生成符合MMAction2官方要求的特征文件。
"""

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path
import tempfile
import shutil
import numpy as np
import pickle
import torch
from typing import Dict, List, Tuple

def download_slowonly_checkpoint():
    """下载SlowOnly R50 Kinetics-400预训练模型"""
    checkpoint_url = ('https://download.openmmlab.com/mmaction/v1.0/recognition/slowonly/'
                     'slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-'
                     'rgb/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_'
                     'kinetics400-rgb_20220901-e7b65fad.pth')
    
    checkpoint_dir = Path('../work_dirs/checkpoints')
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    checkpoint_path = checkpoint_dir / 'slowonly_r50_kinetics400.pth'
    
    if not checkpoint_path.exists():
        print(f"下载SlowOnly R50预训练模型...")
        cmd = f"wget -O {checkpoint_path} {checkpoint_url}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"下载失败: {result.stderr}")
            return None
        print(f"模型下载完成: {checkpoint_path}")
    else:
        print(f"使用已存在的模型: {checkpoint_path}")
    
    return str(checkpoint_path)

def create_slowonly_config():
    """创建SlowOnly特征提取配置文件"""
    config_content = '''# SlowOnly R50特征提取配置文件
# 用于BMN时间动作定位任务

# 模型设置
model = dict(
    type='Recognizer3D',
    backbone=dict(
        type='ResNet3dSlowOnly',
        depth=50,
        pretrained=None,
        pretrained2d=False,
        lateral=False,
        num_stages=4,
        conv1_kernel=(1, 7, 7),
        conv1_stride_t=1,
        pool1_stride_t=1,
        spatial_strides=(1, 2, 2, 1),
        norm_cfg=dict(type='BN3d', requires_grad=True)),
    cls_head=dict(
        type='I3DHead',
        num_classes=400,
        in_channels=2048,
        spatial_type='avg',
        dropout_ratio=0.5,
        init_std=0.01),
    data_preprocessor=dict(
        type='ActionDataPreprocessor',
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        format_shape='NCTHW'))

# 数据集设置
dataset_type = 'VideoDataset'
data_root = ''
data_root_val = ''
ann_file_train = ''
ann_file_val = ''
ann_file_test = ''

# 测试数据流水线
test_pipeline = [
    dict(type='DecordInit', io_backend='disk'),
    dict(
        type='SampleFrames',
        clip_len=8,
        frame_interval=8,
        num_clips=1,
        test_mode=True),
    dict(type='DecordDecode'),
    dict(type='Resize', scale=(-1, 256)),
    dict(type='CenterCrop', crop_size=224),
    dict(type='FormatShape', input_format='NCTHW'),
    dict(type='PackActionInputs')
]

# 测试数据加载器
test_dataloader = dict(
    batch_size=1,
    num_workers=2,  # 减少worker数量以节省内存
    persistent_workers=False,  # 关闭persistent workers以节省内存
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_test,
        data_prefix=dict(video=data_root_val),
        pipeline=test_pipeline,
        test_mode=True))

# 测试配置
test_cfg = dict(type='TestLoop')

# 测试评估器
test_evaluator = dict(type='DumpResults', out_file_path='result.pkl')

# 默认运行时设置
default_scope = 'mmaction'
default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=20, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=3, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='ActionVisualizer', vis_backends=vis_backends, name='visualizer')

log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)
log_level = 'INFO'
load_from = None
resume = False
'''
    
    config_path = Path('slowonly_r50_feature_extraction_config.py')
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    return str(config_path)

def load_reconstructed_annotations(json_file: str) -> Dict:
    """加载重构的标注文件"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def create_video_list(video_names: List[str], video_root: str) -> str:
    """创建视频列表文件"""
    video_list_path = Path('video_list_temp.txt')

    with open(video_list_path, 'w', encoding='utf-8') as f:
        for video_name in video_names:
            # 首先尝试原始名称
            video_path = Path(video_root) / f"{video_name}.mp4"
            if video_path.exists():
                f.write(f"{video_name}.mp4\n")
            else:
                # 尝试添加_decrypted_roi后缀
                video_path_with_suffix = Path(video_root) / f"{video_name}_decrypted_roi.mp4"
                if video_path_with_suffix.exists():
                    f.write(f"{video_name}_decrypted_roi.mp4\n")
                else:
                    print(f"警告: 视频文件不存在: {video_path} 或 {video_path_with_suffix}")

    return str(video_list_path)

def extract_features_for_videos(video_names: List[str], video_root: str, output_dir: str) -> bool:
    """为指定视频提取SlowOnly特征"""
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 下载预训练模型
    checkpoint_path = download_slowonly_checkpoint()
    if checkpoint_path is None:
        print("无法下载预训练模型，退出")
        return False
    
    # 创建配置文件
    config_path = create_slowonly_config()
    
    # 创建视频列表
    video_list_path = create_video_list(video_names, video_root)
    
    # 构建特征提取命令
    cmd = [
        'python', '../mmaction2/tools/misc/clip_feature_extraction.py',
        config_path,
        checkpoint_path,
        str(output_path / 'features_raw'),
        '--video-list', video_list_path,
        '--video-root', video_root,
        '--long-video-mode',
        '--clip-interval', '32',  # 增加clip间隔以减少内存使用
        '--frame-interval', '2',  # 增加帧间隔以减少内存使用
        '--spatial-type', 'avg',  # 空间维度平均池化
        '--temporal-type', 'keep'  # 保持时间维度，不进行时间池化
    ]
    
    print("开始提取SlowOnly特征...")
    print(f"命令: {' '.join(cmd)}")
    
    # 执行特征提取
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("特征提取完成!")
            if result.stdout.strip():
                print("输出:", result.stdout)
        else:
            print("特征提取失败!")
            print("错误:", result.stderr)
            return False
            
    except Exception as e:
        print(f"执行特征提取时出错: {e}")
        return False
    
    # 清理临时文件
    try:
        os.remove(config_path)
        os.remove(video_list_path)
    except:
        pass
    
    return True

def pool_feature_to_100_steps(data, num_proposals=100, num_sample_bins=3, pool_type='mean'):
    """
    将任意长度的特征池化到100个时间步
    
    Args:
        data (np.ndarray): 输入特征，形状为 [T, D] 其中T是时间步数，D是特征维度
        num_proposals (int): 目标时间步数，默认100
        num_sample_bins (int): 每个时间步采样的点数，默认3
        pool_type (str): 池化类型，'mean'或'max'
    
    Returns:
        np.ndarray: 池化后的特征，形状为 [num_proposals, D]
    """
    if len(data) == 1:
        # 如果只有一个时间步，复制到所有时间步
        return np.tile(data, (num_proposals, 1))
    
    # 确保数据是2D的 (time_steps, feature_dim)
    if len(data.shape) != 2:
        raise ValueError(f"期望2D特征数据，得到形状: {data.shape}")
    
    time_steps, feature_dim = data.shape
    
    if time_steps <= num_proposals:
        # 如果时间步数不足，使用插值
        from scipy import interpolate
        x_old = np.linspace(0, 1, time_steps)
        x_new = np.linspace(0, 1, num_proposals)
        
        pooled_data = np.zeros((num_proposals, feature_dim))
        for i in range(feature_dim):
            f = interpolate.interp1d(x_old, data[:, i], kind='linear')
            pooled_data[:, i] = f(x_new)
        
        return pooled_data
    
    # 计算每个proposal对应的时间范围
    proposal_duration = time_steps / num_proposals
    pooled_data = np.zeros((num_proposals, feature_dim))
    
    for i in range(num_proposals):
        start_idx = int(i * proposal_duration)
        end_idx = int((i + 1) * proposal_duration)
        end_idx = min(end_idx, time_steps)
        
        if start_idx >= end_idx:
            # 边界情况处理
            if start_idx > 0:
                pooled_data[i] = data[start_idx - 1]
            else:
                pooled_data[i] = data[0]
        else:
            # 在时间范围内进行池化
            segment_data = data[start_idx:end_idx]
            if pool_type == 'mean':
                pooled_data[i] = np.mean(segment_data, axis=0)
            elif pool_type == 'max':
                pooled_data[i] = np.max(segment_data, axis=0)
            else:
                raise ValueError(f"不支持的池化类型: {pool_type}")
    
    return pooled_data

def postprocess_features(raw_features_dir: str, output_dir: str):
    """后处理特征为BMN格式"""
    raw_path = Path(raw_features_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"开始后处理特征...")
    print(f"输入目录: {raw_path}")
    print(f"输出目录: {output_path}")
    
    if not raw_path.exists():
        print(f"原始特征目录不存在: {raw_path}")
        return False
    
    # 处理所有pkl文件
    pkl_files = list(raw_path.glob('*.pkl'))
    if not pkl_files:
        print(f"在 {raw_path} 中未找到pkl文件")
        return False
    
    for pkl_file in pkl_files:
        try:
            # 加载特征
            with open(pkl_file, 'rb') as f:
                features = pickle.load(f)
            
            # 处理特征数据
            if isinstance(features, torch.Tensor):
                features = features.cpu().numpy()
            elif isinstance(features, list):
                features = np.array(features)
            
            print(f"处理 {pkl_file.name}: 原始特征形状 {features.shape}")
            
            # 处理特征维度
            if len(features.shape) == 3:
                # 形状是 (batch, feature_dim, time_steps)
                batch_size, feature_dim, time_steps = features.shape
                print(f"检测到3D特征: batch={batch_size}, feature_dim={feature_dim}, time_steps={time_steps}")
                
                # 平均所有batch/clips
                features = features.mean(axis=0)  # 结果: (feature_dim, time_steps)
                print(f"平均后特征形状: {features.shape}")
                
                # 转置为 (time_steps, feature_dim) 以便池化
                features = features.T  # 结果: (time_steps, feature_dim)
                
            elif len(features.shape) == 2:
                # 如果是2D，检查哪个维度是特征维度
                if features.shape[0] == 2048:
                    # (feature_dim, time_steps) -> (time_steps, feature_dim)
                    features = features.T
                # 如果features.shape[1] == 2048，则已经是正确格式
            
            # 池化到100个时间步
            pooled_features = pool_feature_to_100_steps(features, num_proposals=100)
            print(f"池化后特征形状: {pooled_features.shape}")
            
            # 验证特征维度
            if pooled_features.shape != (100, 2048):
                print(f"警告: 特征形状不符合预期 {pooled_features.shape}，期望 (100, 2048)")
            
            # 保存为CSV格式
            video_name = pkl_file.stem
            csv_file = output_path / f"{video_name}.csv"
            np.savetxt(csv_file, pooled_features, delimiter=',', fmt='%.6f')
            
            print(f"✅ 保存特征文件: {csv_file}")
            
        except Exception as e:
            print(f"❌ 处理 {pkl_file} 时出错: {e}")
            continue
    
    print("特征后处理完成!")
    return True

def validate_features(features_dir: str) -> bool:
    """验证生成的特征文件格式"""
    features_path = Path(features_dir)
    if not features_path.exists():
        print(f"❌ 特征目录不存在: {features_path}")
        return False

    csv_files = list(features_path.glob('*.csv'))
    if not csv_files:
        print(f"❌ 在 {features_path} 中未找到CSV特征文件")
        return False

    print(f"🔍 验证 {len(csv_files)} 个特征文件...")

    valid_count = 0
    for csv_file in csv_files:
        try:
            features = np.loadtxt(csv_file, delimiter=',')
            if features.shape == (100, 2048):
                valid_count += 1
            else:
                print(f"⚠️  {csv_file.name}: 形状不正确 {features.shape}，期望 (100, 2048)")
        except Exception as e:
            print(f"❌ {csv_file.name}: 读取失败 - {e}")

    print(f"✅ {valid_count}/{len(csv_files)} 个特征文件格式正确")
    return valid_count == len(csv_files)

def generate_bmn_config_template(features_dir: str, train_json: str, val_json: str):
    """生成BMN配置文件模板"""
    config_template = f'''# BMN配置文件模板 - 基于重构数据集
# 自动生成于SlowOnly特征提取脚本

_base_ = [
    '../mmaction2/configs/_base_/models/bmn_400x100.py',
    '../mmaction2/configs/_base_/default_runtime.py'
]

# 模型设置 - 修改特征维度为2048
model = dict(
    type='BMN',
    temporal_dim=100,           # 时间维度：100个时间步
    boundary_ratio=0.5,
    num_samples=32,
    num_samples_per_bin=3,
    feat_dim=2048,              # SlowOnly R50特征维度
    soft_nms_alpha=0.4,
    soft_nms_low_threshold=0.5,
    soft_nms_high_threshold=0.9,
    post_process_top_k=100)

# 数据集设置
dataset_type = 'ActivityNetDataset'
data_root = '{features_dir}'
data_root_val = '{features_dir}'
ann_file_train = '{train_json}'
ann_file_val = '{val_json}'
ann_file_test = '{val_json}'

# 训练数据流水线
train_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', ))
]

# 验证数据流水线
val_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', ))
]

# 测试数据流水线
test_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        type='PackLocalizationInputs',
        keys=(),
        meta_keys=('video_name', ))
]

# 数据加载器
train_dataloader = dict(
    batch_size=8,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_train,
        data_prefix=dict(video=data_root),
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=1,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_val,
        data_prefix=dict(video=data_root_val),
        pipeline=val_pipeline,
        test_mode=True))

test_dataloader = dict(
    batch_size=1,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_test,
        data_prefix=dict(video=data_root_val),
        pipeline=test_pipeline,
        test_mode=True))

# 优化器
optim_wrapper = dict(
    optimizer=dict(type='Adam', lr=0.001, weight_decay=1e-4),
    clip_grad=dict(max_norm=40, norm_type=2))

# 学习率调度器
param_scheduler = [
    dict(type='MultiStepLR', begin=0, end=9, by_epoch=True, milestones=[7], gamma=0.1)
]

# 训练配置
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=9, val_begin=1, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# 工作目录
work_dir = '../work_dirs/bmn_reconstructed_slowonly'

# 评估器
test_evaluator = dict(
    type='ANetMetric',
    metric_type='AR@AN',
    dump_config=dict(out=f'{{work_dir}}/results.json', output_format='json'))
val_evaluator = test_evaluator

# 默认钩子
default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=10, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=1, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

# 环境配置
env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='ActionVisualizer', vis_backends=vis_backends, name='visualizer')

log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)
log_level = 'INFO'
load_from = None
resume = False
'''

    config_path = Path('bmn_reconstructed_config.py')
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_template)

    print(f"✅ BMN配置文件模板已生成: {config_path}")
    return str(config_path)

def main():
    parser = argparse.ArgumentParser(description='基于重构JSON文件的SlowOnly特征提取')
    parser.add_argument('--train-json',
                       default='../data/MultiClassTAD/multiclass_tad_train_reconstructed.json',
                       help='训练集重构JSON文件路径')
    parser.add_argument('--val-json',
                       default='../data/MultiClassTAD/multiclass_tad_val_reconstructed.json',
                       help='验证集重构JSON文件路径')
    parser.add_argument('--raw-videos-root',
                       default='../data/raw_videos',
                       help='原始视频文件根目录路径')
    parser.add_argument('--output-dir',
                       default='../data/MultiClassTAD/features_slowonly_reconstructed',
                       help='输出特征文件目录')
    parser.add_argument('--skip-extraction', action='store_true',
                       help='跳过特征提取步骤（如果已经提取过）')
    parser.add_argument('--skip-postprocess', action='store_true',
                       help='跳过特征后处理步骤')
    parser.add_argument('--only-validate', action='store_true',
                       help='只运行验证步骤')

    args = parser.parse_args()

    print("🎯 基于重构JSON文件的SlowOnly特征提取")
    print("=" * 60)
    print(f"训练集JSON: {args.train_json}")
    print(f"验证集JSON: {args.val_json}")
    print(f"原始视频目录: {args.raw_videos_root}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)

    # 如果只验证，直接跳到验证步骤
    if args.only_validate:
        print("🔍 只运行验证步骤...")
        features_dir = Path(args.output_dir) / 'features_csv'
        success = validate_features(str(features_dir))
        if success:
            print("✅ 特征文件验证通过!")
        else:
            print("❌ 特征文件验证失败!")
        return success

    # 检查输入文件
    if not Path(args.train_json).exists():
        print(f"❌ 训练集JSON文件不存在: {args.train_json}")
        return False

    if not Path(args.val_json).exists():
        print(f"❌ 验证集JSON文件不存在: {args.val_json}")
        return False

    if not args.skip_extraction and not Path(args.raw_videos_root).exists():
        print(f"❌ 原始视频目录不存在: {args.raw_videos_root}")
        return False

    # 加载重构的标注数据
    print("\n📖 加载重构标注数据...")
    train_data = load_reconstructed_annotations(args.train_json)
    val_data = load_reconstructed_annotations(args.val_json)

    # 收集所有需要提取特征的视频名称
    all_video_names = set()
    all_video_names.update(train_data.keys())
    all_video_names.update(val_data.keys())

    print(f"找到 {len(all_video_names)} 个需要提取特征的视频")

    # 检查视频文件是否存在
    if not args.skip_extraction:
        missing_videos = []
        found_videos = []
        for video_name in all_video_names:
            video_path = Path(args.raw_videos_root) / f"{video_name}.mp4"
            video_path_with_suffix = Path(args.raw_videos_root) / f"{video_name}_decrypted_roi.mp4"

            if video_path.exists():
                found_videos.append(video_name)
            elif video_path_with_suffix.exists():
                found_videos.append(video_name)
            else:
                missing_videos.append(video_name)

        print(f"✅ 找到 {len(found_videos)} 个视频文件")

        if missing_videos:
            print(f"⚠️  警告: {len(missing_videos)} 个视频文件不存在:")
            for video in missing_videos[:5]:  # 只显示前5个
                print(f"   - {video}.mp4")
            if len(missing_videos) > 5:
                print(f"   ... 还有 {len(missing_videos) - 5} 个")

            if len(found_videos) == 0:
                print("❌ 没有找到任何视频文件，无法继续")
                return False

    # 步骤1: 特征提取
    if not args.skip_extraction:
        print("\n🔧 步骤1: 提取SlowOnly特征")
        success = extract_features_for_videos(
            list(all_video_names),
            args.raw_videos_root,
            args.output_dir
        )
        if not success:
            print("❌ 特征提取失败")
            return False
    else:
        print("\n⏭️  跳过特征提取步骤")

    # 步骤2: 特征后处理
    if not args.skip_postprocess:
        print("\n🔄 步骤2: 后处理特征为BMN格式")
        raw_features_dir = Path(args.output_dir) / 'features_raw'
        final_features_dir = Path(args.output_dir) / 'features_csv'

        success = postprocess_features(str(raw_features_dir), str(final_features_dir))
        if not success:
            print("❌ 特征后处理失败")
            return False
    else:
        print("\n⏭️  跳过特征后处理步骤")

    # 步骤3: 验证特征
    print("\n✅ 步骤3: 验证特征格式")
    final_features_dir = Path(args.output_dir) / 'features_csv'
    success = validate_features(str(final_features_dir))
    if not success:
        print("❌ 特征验证失败")
        return False

    # 步骤4: 生成BMN配置文件模板
    print("\n📝 步骤4: 生成BMN配置文件模板")
    config_path = generate_bmn_config_template(
        str(final_features_dir),
        args.train_json,
        args.val_json
    )

    # 完成
    print("\n" + "=" * 60)
    print("🎉 SlowOnly特征提取完成!")
    print("=" * 60)
    print(f"\n📁 生成的文件:")
    print(f"   - 特征文件: {args.output_dir}/features_csv/")
    print(f"   - BMN配置: {config_path}")
    print("\n🚀 下一步:")
    print("   1. 检查生成的BMN配置文件并根据需要调整")
    print("   2. 激活mmaction2-tad conda环境")
    print("   3. 运行BMN训练:")
    print(f"      cd ../mmaction2")
    print(f"      python tools/train.py ../{config_path}")

    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
